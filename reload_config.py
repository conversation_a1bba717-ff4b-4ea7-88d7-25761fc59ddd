#!/usr/bin/env python3
"""
Configuration reload utility script.

This script provides utilities to test and reload configuration
when environment variables change.
"""

import os
import sys
from typing import Dict, Any

# Add the project root to Python path
sys.path.insert(0, ".")

from src.utils.config import get_settings, reload_settings, clear_settings_cache


def print_current_settings() -> None:
    """Print current configuration settings."""
    print("=== Current Configuration ===")
    settings = get_settings()
    
    # Print key configuration values
    config_items = [
        ("debug", settings.debug),
        ("host", settings.host),
        ("port", settings.port),
        ("log_level", settings.log_level),
        ("database_url", settings.database_url[:50] + "..." if len(settings.database_url) > 50 else settings.database_url),
        ("google_calendar_webhook_url", settings.google_calendar_webhook_url),
        ("redis_url", settings.redis_url),
    ]
    
    for key, value in config_items:
        print(f"  {key}: {value}")
    print()


def test_env_reload() -> None:
    """Test environment variable reloading."""
    print("=== Testing Environment Variable Reload ===")
    
    # Show current settings
    print("1. Current settings:")
    print_current_settings()
    
    # Show environment variables
    print("2. Current environment variables:")
    env_vars = [
        "DEBUG",
        "HOST", 
        "PORT",
        "LOG_LEVEL",
        "GOOGLE_CALENDAR_WEBHOOK_URL",
        "DATABASE_URL",
        "REDIS_URL"
    ]
    
    for var in env_vars:
        value = os.environ.get(var, "NOT SET")
        print(f"  {var}: {value}")
    print()
    
    # Test cache behavior
    print("3. Testing cache behavior:")
    settings1 = get_settings()
    settings2 = get_settings()
    print(f"  Same instance (cached): {settings1 is settings2}")
    
    # Test reload
    print("4. Testing reload:")
    settings3 = reload_settings()
    print(f"  New instance after reload: {settings1 is not settings3}")
    
    # Verify settings are still the same object after reload
    settings4 = get_settings()
    print(f"  Cached after reload: {settings3 is settings4}")
    print()


def simulate_env_change() -> None:
    """Simulate changing environment variables and reloading."""
    print("=== Simulating Environment Change ===")
    
    # Show current port
    current_settings = get_settings()
    print(f"1. Current port: {current_settings.port}")
    
    # Change environment variable
    original_port = os.environ.get("PORT", "8000")
    new_port = "9999"
    os.environ["PORT"] = new_port
    print(f"2. Changed PORT environment variable to: {new_port}")
    
    # Show settings without reload (should still be cached)
    cached_settings = get_settings()
    print(f"3. Port from cached settings: {cached_settings.port}")
    
    # Reload settings
    print("4. Reloading settings...")
    reloaded_settings = reload_settings()
    print(f"5. Port from reloaded settings: {reloaded_settings.port}")
    
    # Restore original value
    if original_port == "8000":
        os.environ.pop("PORT", None)
    else:
        os.environ["PORT"] = original_port
    print(f"6. Restored PORT environment variable to: {original_port}")
    
    # Final reload
    final_settings = reload_settings()
    print(f"7. Port after restoration: {final_settings.port}")
    print()


def main() -> None:
    """Main function to run all tests."""
    print("Configuration Reload Testing")
    print("=" * 50)
    
    try:
        print_current_settings()
        test_env_reload()
        simulate_env_change()
        
        print("=== Summary ===")
        print("✅ Configuration caching is working properly")
        print("✅ reload_settings() forces cache refresh")
        print("✅ Environment variable changes are detected after reload")
        print("\nTo reload configuration in your application:")
        print("  from src.utils.config import reload_settings")
        print("  settings = reload_settings()")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
