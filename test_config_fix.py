#!/usr/bin/env python3
"""
Test script to verify the configuration fix works properly.

This script tests the configuration loading and reloading functionality
to ensure environment variables are properly loaded after changes.
"""

import os
import sys
import time
from typing import Dict, Any

# Add the project root to Python path
sys.path.insert(0, ".")

from src.utils.config import get_settings, reload_settings, clear_settings_cache, validate_configuration


def test_basic_functionality():
    """Test basic configuration functionality."""
    print("=== Testing Basic Configuration Functionality ===")
    
    # Test initial load
    print("1. Loading initial configuration...")
    settings1 = get_settings()
    print(f"   Initial port: {settings1.port}")
    print(f"   Initial debug: {settings1.debug}")
    
    # Test caching
    print("2. Testing caching...")
    settings2 = get_settings()
    is_cached = settings1 is settings2
    print(f"   Same instance (cached): {is_cached}")
    assert is_cached, "Settings should be cached"
    
    # Test cache info
    if hasattr(get_settings, 'cache_info'):
        cache_info = get_settings.cache_info()
        print(f"   Cache info: hits={cache_info.hits}, misses={cache_info.misses}")
    
    print("✅ Basic functionality test passed\n")


def test_environment_change_detection():
    """Test that environment variable changes are detected after reload."""
    print("=== Testing Environment Change Detection ===")
    
    # Get current settings
    current_settings = get_settings()
    original_port = current_settings.port
    original_debug = current_settings.debug
    
    print(f"1. Original settings - Port: {original_port}, Debug: {original_debug}")
    
    # Change environment variables
    new_port = "9876"
    new_debug = "false" if original_debug else "true"
    
    # Store original values for restoration
    original_port_env = os.environ.get("PORT")
    original_debug_env = os.environ.get("DEBUG")
    
    try:
        # Set new environment variables
        os.environ["PORT"] = new_port
        os.environ["DEBUG"] = new_debug
        print(f"2. Changed environment - PORT: {new_port}, DEBUG: {new_debug}")
        
        # Test that cached settings haven't changed
        cached_settings = get_settings()
        print(f"3. Cached settings - Port: {cached_settings.port}, Debug: {cached_settings.debug}")
        assert cached_settings.port == original_port, "Cached settings should not change"
        assert cached_settings.debug == original_debug, "Cached settings should not change"
        
        # Reload settings
        print("4. Reloading configuration...")
        reloaded_settings = reload_settings()
        print(f"5. Reloaded settings - Port: {reloaded_settings.port}, Debug: {reloaded_settings.debug}")
        
        # Verify changes were detected
        assert reloaded_settings.port == int(new_port), f"Port should be {new_port}, got {reloaded_settings.port}"
        expected_debug = new_debug.lower() == "true"
        assert reloaded_settings.debug == expected_debug, f"Debug should be {expected_debug}, got {reloaded_settings.debug}"
        
        # Test that subsequent calls use the new cached values
        subsequent_settings = get_settings()
        assert subsequent_settings is reloaded_settings, "Subsequent calls should return cached reloaded settings"
        
        print("✅ Environment change detection test passed\n")
        
    finally:
        # Restore original environment variables
        if original_port_env is not None:
            os.environ["PORT"] = original_port_env
        else:
            os.environ.pop("PORT", None)
            
        if original_debug_env is not None:
            os.environ["DEBUG"] = original_debug_env
        else:
            os.environ.pop("DEBUG", None)
        
        # Reload to restore original settings
        reload_settings()
        print("6. Environment variables restored")


def test_validation_function():
    """Test the configuration validation function."""
    print("=== Testing Configuration Validation ===")
    
    try:
        diagnostics = validate_configuration()
        
        print("1. Validation completed successfully")
        print(f"   .env file exists: {diagnostics.get('env_file_exists')}")
        print(f"   Environment variables found: {len([v for v in diagnostics.get('environment_variables', {}).values() if v is not None])}")
        print(f"   Loaded settings keys: {list(diagnostics.get('loaded_settings', {}).keys())}")
        
        if diagnostics.get('cache_info'):
            cache_info = diagnostics['cache_info']
            print(f"   Cache hits: {cache_info.get('hits', 0)}, misses: {cache_info.get('misses', 0)}")
        
        if diagnostics.get('errors'):
            print(f"   Errors: {diagnostics['errors']}")
        else:
            print("   No errors detected")
        
        print("✅ Configuration validation test passed\n")
        
    except Exception as e:
        print(f"❌ Configuration validation test failed: {e}")
        raise


def test_cache_clearing():
    """Test cache clearing functionality."""
    print("=== Testing Cache Clearing ===")
    
    # Load settings to populate cache
    settings1 = get_settings()
    
    if hasattr(get_settings, 'cache_info'):
        cache_info_before = get_settings.cache_info()
        print(f"1. Cache before clearing: hits={cache_info_before.hits}, misses={cache_info_before.misses}")
    
    # Clear cache
    clear_settings_cache()
    print("2. Cache cleared")
    
    # Load settings again
    settings2 = get_settings()
    
    if hasattr(get_settings, 'cache_info'):
        cache_info_after = get_settings.cache_info()
        print(f"3. Cache after reload: hits={cache_info_after.hits}, misses={cache_info_after.misses}")
    
    # Verify new instance was created
    assert settings1 is not settings2, "New settings instance should be created after cache clear"
    
    # But values should be the same (assuming no env changes)
    assert settings1.port == settings2.port, "Settings values should be the same"
    assert settings1.debug == settings2.debug, "Settings values should be the same"
    
    print("✅ Cache clearing test passed\n")


def main():
    """Run all configuration tests."""
    print("Configuration Fix Verification")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_environment_change_detection()
        test_validation_function()
        test_cache_clearing()
        
        print("🎉 All tests passed!")
        print("\nConfiguration fix summary:")
        print("✅ Settings are properly cached with @lru_cache")
        print("✅ reload_settings() forces cache refresh")
        print("✅ Environment variable changes are detected after reload")
        print("✅ Cache can be manually cleared when needed")
        print("✅ Validation function provides diagnostic information")
        print("\nThe configuration loading issue has been fixed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
