# Configuration Reload Guide

This guide explains how to use the fixed configuration system that properly handles environment variable changes without requiring server restarts.

## Problem Fixed

The original configuration system had the following issues:
- Settings were not properly cached, causing performance issues
- Environment variable changes were not detected after server startup
- No way to reload configuration without restarting the service
- Debug print statements in production code

## Solution Implemented

### 1. Proper Caching with LRU Cache

The `get_settings()` function now uses `@lru_cache()` decorator to cache settings:

```python
@lru_cache()
def get_settings() -> Settings:
    """Get application settings with caching."""
    return Settings()
```

### 2. Configuration Reload Functions

New functions have been added to handle configuration reloading:

```python
# Force reload configuration from environment variables
def reload_settings() -> Settings:
    """Force reload of application settings by clearing the cache."""
    get_settings.cache_clear()
    return get_settings()

# Clear cache without reloading
def clear_settings_cache() -> None:
    """Clear the settings cache without reloading."""
    get_settings.cache_clear()

# Validate configuration and get diagnostics
def validate_configuration() -> Dict[str, Any]:
    """Validate current configuration and return diagnostic information."""
    # Returns detailed diagnostics about configuration loading
```

### 3. API Endpoints for Runtime Configuration Management

Two new API endpoints have been added to the health router:

#### POST `/api/v1/health/config/reload`
Reloads configuration from environment variables and returns change summary.

**Example Response:**
```json
{
  "status": "success",
  "timestamp": "2024-01-15T10:30:00Z",
  "user_id": "user123",
  "changes_detected": [
    {
      "setting": "port",
      "old_value": 8000,
      "new_value": 9000
    }
  ],
  "message": "Configuration reloaded successfully. 1 changes detected."
}
```

#### GET `/api/v1/health/config/validate`
Validates current configuration and returns diagnostic information.

**Example Response:**
```json
{
  "status": "success",
  "timestamp": "2024-01-15T10:30:00Z",
  "user_id": "user123",
  "diagnostics": {
    "env_file_exists": true,
    "env_file_path": "/path/to/.env",
    "environment_variables": {
      "PORT": "8000",
      "DEBUG": "true",
      "SECRET_KEY": "abc12345..."
    },
    "loaded_settings": {
      "debug": true,
      "host": "0.0.0.0",
      "port": 8000
    },
    "cache_info": {
      "hits": 5,
      "misses": 1,
      "maxsize": 128,
      "currsize": 1
    },
    "errors": []
  }
}
```

## Usage Examples

### 1. Programmatic Configuration Reload

```python
from src.utils.config import get_settings, reload_settings

# Get current settings (cached)
settings = get_settings()
print(f"Current port: {settings.port}")

# Change environment variable
import os
os.environ['PORT'] = '9000'

# Settings are still cached
cached_settings = get_settings()
print(f"Cached port: {cached_settings.port}")  # Still old value

# Force reload
new_settings = reload_settings()
print(f"Reloaded port: {new_settings.port}")  # New value
```

### 2. API-based Configuration Reload

```bash
# Reload configuration via API
curl -X POST "http://localhost:8000/api/v1/health/config/reload" \
  -H "Authorization: Bearer YOUR_API_KEY"

# Validate configuration via API
curl "http://localhost:8000/api/v1/health/config/validate" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### 3. Development Workflow

1. **Change environment variables** in your `.env` file or export them:
   ```bash
   export PORT=9000
   export DEBUG=false
   ```

2. **Reload configuration** without restarting the server:
   ```python
   # In your application code
   from src.utils.config import reload_settings
   settings = reload_settings()
   ```

   Or via API:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/health/config/reload" \
     -H "Authorization: Bearer YOUR_API_KEY"
   ```

3. **Verify changes** took effect:
   ```python
   from src.utils.config import get_settings
   settings = get_settings()
   print(f"New port: {settings.port}")
   ```

## Testing the Fix

Use the provided test scripts to verify the configuration system works correctly:

```bash
# Run simple test
python simple_config_test.py

# Run comprehensive test
python reload_config.py
```

## Best Practices

1. **Use reload_settings() sparingly** - Only call it when you know environment variables have changed
2. **Monitor cache performance** - Use the cache_info in diagnostics to ensure caching is working
3. **Validate configuration** - Use the validation endpoint to debug configuration issues
4. **Handle reload errors** - Always wrap reload calls in try-catch blocks in production code

## Migration Notes

- **No breaking changes** - Existing code using `get_settings()` continues to work
- **Performance improvement** - Settings are now properly cached
- **New capabilities** - Runtime configuration reload without server restart
- **Better debugging** - Validation function provides detailed diagnostics

The configuration system is now production-ready with proper caching, reload capabilities, and comprehensive error handling.
