#!/usr/bin/env python3
"""Simple configuration test."""

import os
import sys
sys.path.append(".")

try:
    print("Testing configuration loading...")
    
    # Test basic import
    from src.utils.config import get_settings, reload_settings
    print("✅ Import successful")
    
    # Test basic loading
    settings = get_settings()
    print(f"✅ Settings loaded - Port: {settings.port}")
    
    # Test caching
    settings2 = get_settings()
    print(f"✅ Caching works: {settings is settings2}")
    
    # Test environment change
    original_port = settings.port
    os.environ['PORT'] = '9999'
    
    # Should still be cached
    cached = get_settings()
    print(f"✅ Still cached after env change: {cached.port == original_port}")
    
    # Test reload
    reloaded = reload_settings()
    print(f"✅ Reloaded with new value: {reloaded.port}")
    
    print("🎉 All tests passed! Configuration fix is working.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
