"""
Google Calendar Adapter for the Trigger Service.

This module implements the Google Calendar adapter that monitors calendar events
via webhooks and transforms them into standardized trigger events.
"""

import asyncio
import json
import os
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Set
from uuid import UUID

import httpx
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from src.core.auth_client import AuthClient
from src.utils.config import get_settings
from src.utils.logger import get_logger
from src.utils.retry import RetryHandler, RetryableError

logger = get_logger(__name__)


class GoogleCalendarError(Exception):
    """Base exception for Google Calendar adapter errors."""

    pass


class GoogleCalendarAuthError(GoogleCalendarError):
    """Authentication-related errors."""

    pass


class GoogleCalendarAPIError(GoogleCalendarError, RetryableError):
    """API-related errors that should trigger retry."""

    pass


class GoogleCalendarAdapter(BaseTriggerAdapter):
    """
    Google Calendar adapter for monitoring calendar events.

    This adapter integrates with Google Calendar API to:
    - Set up webhook subscriptions for calendar events
    - Process incoming webhook events
    - Transform calendar events into standardized trigger events
    - Manage subscription lifecycle and renewal
    """

    def __init__(self):
        """Initialize the Google Calendar adapter."""
        super().__init__("google_calendar")
        self.settings = get_settings()

        # Debug: Log the actual webhook URL being loaded
        logger.info(
            f"🔍 DEBUG: Loaded webhook URL: {getattr(self.settings, 'google_calendar_webhook_url', 'NOT_FOUND')}"
        )
        logger.info(f"🔍 DEBUG: Settings type: {type(self.settings)}")
        logger.info(
            f"🔍 DEBUG: All settings attributes: {[attr for attr in dir(self.settings) if not attr.startswith('_')]}"
        )
        # TEMPORARY: Comment out AuthClient until Auth service is ready
        # self.auth_client = AuthClient()
        self.retry_handler = RetryHandler(
            retryable_exceptions=[GoogleCalendarAPIError, HttpError, ConnectionError]
        )
        # Multi-user webhook and polling management (based on your webhook implementation)
        self._webhook_subscriptions: Dict[UUID, Dict[str, Any]] = {}
        self._polling_tasks: Dict[UUID, asyncio.Task] = {}
        self._polling_states: Dict[UUID, Dict[str, Any]] = {}

        # Multi-user credential management
        self._user_credentials: Dict[str, Credentials] = {}
        self._user_services: Dict[str, Any] = {}

        # Channel management (inspired by your active_channels)
        self._active_channels: Dict[str, Dict[str, Any]] = {}

        logger.info(
            "🚀 Google Calendar Adapter initialized",
            features=[
                "multi-user support",
                "webhook + polling hybrid",
                "credential caching",
            ],
        )

    @property
    def supported_event_types(self) -> Set[TriggerEventType]:
        """
        Get the set of event types supported by this adapter.

        Returns:
            Set[TriggerEventType]: Supported event types
        """
        return {
            TriggerEventType.CREATED,
            TriggerEventType.UPDATED,
            TriggerEventType.DELETED,
            TriggerEventType.REMINDER,
        }

    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate Google Calendar adapter configuration.

        Args:
            config: Configuration to validate

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            # Required fields
            required_fields = ["calendar_id"]
            for field in required_fields:
                if field not in config:
                    logger.error(f"Missing required field: {field}")
                    return False

            # Validate calendar_id format
            calendar_id = config["calendar_id"]
            if not isinstance(calendar_id, str) or not calendar_id.strip():
                logger.error("calendar_id must be a non-empty string")
                return False

            # Optional fields validation
            if "event_filters" in config:
                event_filters = config["event_filters"]
                if not isinstance(event_filters, dict):
                    logger.error("event_filters must be a dictionary")
                    return False

            # Validate webhook_ttl if provided
            if "webhook_ttl" in config:
                webhook_ttl = config["webhook_ttl"]
                if not isinstance(webhook_ttl, int) or webhook_ttl <= 0:
                    logger.error("webhook_ttl must be a positive integer")
                    return False

            logger.debug(
                f"Configuration validation passed for calendar_id: {calendar_id}"
            )
            return True

        except Exception as e:
            logger.error(f"Error validating configuration: {str(e)}")
            return False

    async def setup_trigger(self, trigger_config: TriggerConfiguration) -> bool:
        """
        Set up a new Google Calendar trigger with webhook subscription or polling fallback.

        Args:
            trigger_config: Complete trigger configuration

        Returns:
            bool: True if setup was successful, False otherwise
        """
        try:
            logger.info(
                "🔧 Setting up Google Calendar trigger",
                trigger_id=trigger_config.trigger_id,
                user_id=trigger_config.user_id,
                calendar_id=trigger_config.config.get("calendar_id"),
                event_types=trigger_config.event_types,
                use_polling=trigger_config.config.get("use_polling", False),
            )

            # Get user credentials
            credentials = await self._get_user_credentials(trigger_config.user_id)
            if not credentials:
                logger.error(
                    "❌ Failed to get Google Calendar credentials",
                    user_id=trigger_config.user_id,
                    trigger_id=trigger_config.trigger_id,
                )
                return False

            # Create Google Calendar service
            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error("Failed to create Google Calendar service")
                return False

            # Check if polling mode is explicitly requested or if webhook setup fails
            use_polling = trigger_config.config.get("use_polling", False)

            if not use_polling:
                # Try webhook setup first
                subscription_id = await self._create_webhook_subscription(
                    service, trigger_config
                )
                if subscription_id:
                    # Get the resource_id from the active channel
                    channel_info = self._active_channels.get(subscription_id)
                    resource_id = (
                        channel_info.get("resource_id")
                        if channel_info
                        else subscription_id
                    )

                    # Store subscription information
                    self._webhook_subscriptions[trigger_config.trigger_id] = {
                        "subscription_id": subscription_id,
                        "calendar_id": trigger_config.config["calendar_id"],
                        "user_id": trigger_config.user_id,
                        "created_at": datetime.now(),
                        "expires_at": datetime.now()
                        + timedelta(
                            seconds=trigger_config.config.get("webhook_ttl", 3600)
                        ),
                        "resource_id": resource_id,  # Store actual resource_id for cleanup
                    }

                    logger.info(
                        "✅ Google Calendar webhook trigger setup successful",
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,
                        subscription_id=subscription_id,
                        calendar_id=trigger_config.config["calendar_id"],
                    )
                    return True
                else:
                    logger.warning(
                        "⚠️ Webhook setup failed, falling back to polling",
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,
                    )
                    use_polling = True

            if use_polling:
                # Set up polling mode
                success = await self._setup_polling_trigger(trigger_config, service)
                if success:
                    logger.info(
                        "✅ Google Calendar polling trigger setup successful",
                        trigger_id=trigger_config.trigger_id,
                        user_id=trigger_config.user_id,
                        poll_interval=trigger_config.config.get(
                            "poll_interval_seconds", 60
                        ),
                        calendar_id=trigger_config.config["calendar_id"],
                    )
                    return True

            logger.error(
                "Failed to set up trigger with both webhook and polling methods"
            )
            return False

        except Exception as e:
            logger.error(
                f"Failed to setup Google Calendar trigger {trigger_config.trigger_id}",
                error=str(e),
            )
            return False

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove a Google Calendar trigger and its webhook subscription or polling task.

        Args:
            trigger_id: Unique identifier for the trigger to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        try:
            logger.info(f"Removing Google Calendar trigger {trigger_id}")

            # Stop polling task if exists
            if trigger_id in self._polling_tasks:
                task = self._polling_tasks[trigger_id]
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                del self._polling_tasks[trigger_id]
                logger.info(f"Stopped polling task for trigger {trigger_id}")

            # Remove polling state if exists
            if trigger_id in self._polling_states:
                del self._polling_states[trigger_id]

            # Get webhook subscription info
            subscription_info = self._webhook_subscriptions.get(trigger_id)
            if subscription_info:
                # Get user credentials
                credentials = await self._get_user_credentials(
                    subscription_info["user_id"]
                )
                if credentials:
                    # Create Google Calendar service
                    service = await self._create_calendar_service(credentials)
                    if service:
                        # Remove webhook subscription
                        await self._remove_webhook_subscription(
                            service,
                            subscription_info["subscription_id"],
                            subscription_info.get("resource_id"),
                        )

                # Remove from local storage
                del self._webhook_subscriptions[trigger_id]
                logger.info(f"Removed webhook subscription for trigger {trigger_id}")

            logger.info(f"Successfully removed Google Calendar trigger {trigger_id}")
            return True

        except Exception as e:
            logger.error(
                f"Failed to remove Google Calendar trigger {trigger_id}",
                error=str(e),
            )
            return False

    async def process_event(self, raw_event: Dict[str, Any]) -> Optional[TriggerEvent]:
        """
        Process a raw Google Calendar webhook event.
        Enhanced based on your webhook implementation for better multi-user support.

        Args:
            raw_event: Raw event data from Google Calendar webhook

        Returns:
            TriggerEvent: Standardized event data, or None if event should be ignored
        """
        try:
            logger.debug(
                "Processing Google Calendar webhook event", event_data=raw_event
            )

            # Handle verification events
            if raw_event.get("type") == "verification":
                logger.debug("Received verification event, ignoring")
                return None

            # Extract webhook headers for validation (like your implementation)
            headers = raw_event.get("webhook_headers", {})

            # Validate webhook authenticity (enhanced from your implementation)
            if not self._validate_webhook(headers, raw_event):
                logger.warning("Invalid webhook received, ignoring")
                return None

            # Get channel information to identify the user and trigger
            channel_id = headers.get("x-goog-channel-id")
            if not channel_id or channel_id not in self._active_channels:
                logger.warning(f"Unknown or inactive channel: {channel_id}")
                return None

            channel_info = self._active_channels[channel_id]
            user_id = channel_info["user_id"]
            trigger_id = channel_info["trigger_id"]
            calendar_id = channel_info["calendar_id"]

            logger.debug(
                f"Processing webhook for user {user_id}, trigger {trigger_id}",
                channel_id=channel_id,
                calendar_id=calendar_id,
            )

            # Process the calendar event notification (like your process_calendar_event function)
            trigger_events = await self._process_calendar_notification(
                user_id, calendar_id, trigger_id, headers, raw_event
            )

            # Return the first trigger event (or None if no events)
            # In a real implementation, you might want to handle multiple events
            return trigger_events[0] if trigger_events else None

        except Exception as e:
            logger.error(f"Failed to process Google Calendar event", error=str(e))
            return None

    async def _process_calendar_notification(
        self,
        user_id: str,
        calendar_id: str,
        trigger_id: UUID,
        headers: Dict[str, str],
        raw_event: Dict[str, Any],
    ) -> List[TriggerEvent]:
        """
        Process calendar notification and fetch recent events.
        Based on your process_calendar_event function.

        Args:
            user_id: User ID
            calendar_id: Calendar ID
            trigger_id: Trigger ID
            headers: Webhook headers
            raw_event: Raw webhook event

        Returns:
            List[TriggerEvent]: List of trigger events
        """
        try:
            # Get user credentials and service
            credentials = await self._get_user_credentials(user_id)
            if not credentials:
                logger.error(f"No credentials found for user {user_id}")
                return []

            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error(f"Failed to create service for user {user_id}")
                return []

            # Get recent events (like your implementation)
            # The webhook notification doesn't contain event details,
            # only that something changed. We need to fetch recent events.
            from datetime import datetime, timezone, timedelta

            now = datetime.now(timezone.utc)
            # Look for events in the last 5 minutes (like your implementation)
            time_min = (now - timedelta(minutes=5)).isoformat().replace("+00:00", "Z")

            logger.debug(f"Fetching recent events for user {user_id} since {time_min}")

            events_result = await asyncio.to_thread(
                lambda: service.events()
                .list(
                    calendarId=calendar_id,
                    timeMin=time_min,
                    maxResults=10,
                    singleEvents=True,
                    orderBy="startTime",
                )
                .execute()
            )

            events = events_result.get("items", [])
            logger.info(f"Found {len(events)} recent events for user {user_id}")

            trigger_events = []
            for event in events:
                # Create trigger event for each calendar event (like your ACTION part)
                trigger_event = await self._create_trigger_event_from_calendar_event(
                    event, trigger_id, "updated"  # Webhook events are typically updates
                )

                if trigger_event:
                    trigger_events.append(trigger_event)

                    # Log the action (like your implementation)
                    summary = event.get("summary", "No title")
                    start = event["start"].get("dateTime", event["start"].get("date"))
                    event_id = event["id"]
                    created = event.get("created")
                    updated = event.get("updated")

                    logger.info(
                        f"ACTION: Processing event for user {user_id}: '{summary}' starting at {start}",
                        event_id=event_id,
                        created=created,
                        updated=updated,
                        trigger_id=trigger_id,
                    )

            return trigger_events

        except Exception as e:
            logger.error(
                f"Failed to process calendar notification for user {user_id}",
                error=str(e),
                trigger_id=trigger_id,
            )
            return []

    async def _perform_health_check(self) -> bool:
        """
        Perform health check for Google Calendar adapter.

        Returns:
            bool: True if healthy, False otherwise
        """
        try:
            # Check if we can reach Google Calendar API
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://www.googleapis.com/calendar/v3/users/me/calendarList",
                    timeout=10.0,
                )
                # We expect 401 since we're not authenticated, but service should be reachable
                return response.status_code in [200, 401, 403]

        except Exception as e:
            logger.error(f"Google Calendar health check failed", error=str(e))
            return False

    async def _get_user_credentials(self, user_id: str) -> Optional[Credentials]:
        """
        Get Google Calendar credentials for a user with caching support.
        Enhanced based on your webhook implementation for multi-user support.

        Args:
            user_id: ID of the user

        Returns:
            Credentials: Google OAuth2 credentials, or None if not found
        """
        try:
            # Check if we have cached credentials for this user
            if user_id in self._user_credentials:
                credentials = self._user_credentials[user_id]

                # Check if credentials are still valid
                if credentials.valid:
                    return credentials

                # Try to refresh if expired
                if credentials.expired and credentials.refresh_token:
                    logger.info(f"Refreshing cached credentials for user {user_id}")
                    try:
                        await asyncio.to_thread(credentials.refresh, Request())
                        logger.info(
                            f"Successfully refreshed credentials for user {user_id}"
                        )
                        return credentials
                    except Exception as refresh_error:
                        logger.error(
                            f"Failed to refresh cached credentials: {refresh_error}"
                        )
                        # Remove invalid credentials from cache
                        del self._user_credentials[user_id]

            # Load fresh credentials from storage
            credentials = await self._load_user_credentials_from_storage(user_id)
            if credentials:
                # Cache the credentials for future use
                self._user_credentials[user_id] = credentials
                logger.info(f"Cached credentials for user {user_id}")
                return credentials

            return None

        except Exception as e:
            logger.error(f"Failed to get credentials for user {user_id}", error=str(e))
            return None

    async def _load_user_credentials_from_storage(
        self, user_id: str
    ) -> Optional[Credentials]:
        """
        Load user credentials from storage (file-based for now, will be Auth service later).
        Based on your webhook implementation's authenticate() function.

        Args:
            user_id: ID of the user

        Returns:
            Credentials: Google OAuth2 credentials, or None if not found
        """
        try:
            import json
            import os
            import asyncio
            from pathlib import Path

            # Look for user-specific token files first, then fallback to shared token.json
            # This supports multi-user scenarios
            token_paths = [
                Path(f"tokens/token_{user_id}.json"),  # User-specific token
                Path(f"token_{user_id}.json"),  # User-specific in root
                Path("token.json"),  # Shared token (fallback)
                Path("./token.json"),
                Path("../token.json"),
                Path("../../token.json"),
            ]

            token_file = None
            for path in token_paths:
                # Use asyncio to check file existence without blocking
                if await asyncio.to_thread(path.exists):
                    token_file = path
                    logger.debug(f"Found token file for user {user_id}: {token_file}")
                    break

            if not token_file:
                logger.warning(
                    f"No token file found for user {user_id}. "
                    "Please create a token file with Google OAuth2 credentials. "
                    f"Searched paths: {', '.join(str(p) for p in token_paths)}"
                )
                return None

            logger.info(
                f"Loading Google Calendar credentials for user {user_id} from {token_file}"
            )

            # Use asyncio to read file without blocking
            creds_data = await asyncio.to_thread(self._read_json_file, token_file)

            # Create Google OAuth2 credentials object (similar to your authenticate function)
            credentials = Credentials(
                token=creds_data.get("token"),
                refresh_token=creds_data.get("refresh_token"),
                token_uri=creds_data.get(
                    "token_uri", "https://oauth2.googleapis.com/token"
                ),
                client_id=creds_data.get("client_id"),
                client_secret=creds_data.get("client_secret"),
                scopes=creds_data.get(
                    "scopes", ["https://www.googleapis.com/auth/calendar.readonly"]
                ),
            )

            # Check if token needs refresh (like your authenticate function)
            if credentials.expired and credentials.refresh_token:
                logger.info(f"Refreshing expired token for user {user_id}")
                try:
                    # Use asyncio to refresh token without blocking
                    await asyncio.to_thread(credentials.refresh, Request())

                    # Save refreshed token back to file
                    creds_data.update(
                        {
                            "token": credentials.token,
                            "refresh_token": credentials.refresh_token,
                        }
                    )

                    # Use asyncio to write file without blocking
                    await asyncio.to_thread(
                        self._write_json_file, token_file, creds_data
                    )

                    logger.info(f"Token refreshed and saved for user {user_id}")
                except Exception as refresh_error:
                    logger.error(
                        f"Failed to refresh token for user {user_id}: {refresh_error}"
                    )
                    return None
            elif credentials.expired:
                logger.error(
                    f"Token expired and no refresh token available for user {user_id}"
                )
                return None

            logger.info(f"Successfully loaded credentials for user {user_id}")
            return credentials

        except FileNotFoundError:
            logger.error(
                f"Token file not found for user {user_id}. "
                "Please create a token file with Google OAuth2 credentials."
            )
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in token file for user {user_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Failed to load credentials for user {user_id}", error=str(e))
            return None

    async def _create_calendar_service(self, credentials: Credentials):
        """
        Create Google Calendar API service instance.

        Args:
            credentials: Google OAuth2 credentials

        Returns:
            Google Calendar service instance, or None if failed
        """
        try:
            service = build("calendar", "v3", credentials=credentials)
            return service

        except Exception as e:
            logger.error(f"Failed to create Google Calendar service", error=str(e))
            return None

    async def _create_webhook_subscription(
        self, service, trigger_config: TriggerConfiguration
    ) -> Optional[str]:
        """
        Create a webhook subscription for calendar events.
        Enhanced based on your webhook implementation for better multi-user support.

        Args:
            service: Google Calendar service instance
            trigger_config: Trigger configuration

        Returns:
            str: Subscription ID if successful, None otherwise
        """
        try:
            calendar_id = trigger_config.config["calendar_id"]
            webhook_ttl = trigger_config.config.get(
                "webhook_ttl", 604800
            )  # 7 days like your implementation

            # Check if webhook URL is configured
            logger.debug(f"Checking webhook URL configuration...")
            logger.debug(f"Settings object: {self.settings}")
            logger.debug(
                f"Has google_calendar_webhook_url attr: {hasattr(self.settings, 'google_calendar_webhook_url')}"
            )

            if hasattr(self.settings, "google_calendar_webhook_url"):
                logger.debug(
                    f"Raw webhook URL from settings: {self.settings.google_calendar_webhook_url}"
                )

            if (
                not hasattr(self.settings, "google_calendar_webhook_url")
                or not self.settings.google_calendar_webhook_url
            ):
                logger.error("Google Calendar webhook URL not configured")
                return None

            # Validate that webhook URL is HTTPS (required by Google Calendar)
            webhook_url = self.settings.google_calendar_webhook_url
            webhook_url = "https://99b5-103-173-221-201.ngrok-free.app/api/v1/webhooks/google-calendar"
            logger.info(f"Using webhook URL: {webhook_url}")

            if not webhook_url.startswith("https://"):
                logger.error(
                    f"Google Calendar webhook URL must use HTTPS, got: {webhook_url}",
                    extra={
                        "help": "For development, use ngrok to create an HTTPS tunnel: 'ngrok http 8000'"
                    },
                )
                return None

            # Generate unique channel ID (like your implementation)
            import uuid

            channel_id = f"trigger-{trigger_config.trigger_id}-{str(uuid.uuid4())[:8]}"

            # Calculate expiration time (like your webhook implementation)
            from datetime import datetime, timezone

            expiration = int(
                (datetime.now(timezone.utc).timestamp() + webhook_ttl) * 1000
            )

            # Prepare webhook subscription request (enhanced from your implementation)
            body = {
                "id": channel_id,
                "type": "web_hook",
                "address": webhook_url,
                "expiration": str(expiration),
                "params": {
                    "ttl": str(webhook_ttl),
                },
            }

            # Add token for verification if configured (like your implementation)
            webhook_secret = getattr(
                self.settings, "google_calendar_webhook_secret", None
            )
            if webhook_secret:
                body["token"] = webhook_secret

            # Create the subscription with timeout
            result = await asyncio.wait_for(
                self._execute_watch_request(service, calendar_id, body),
                timeout=30.0,  # 30 second timeout
            )

            if result:
                subscription_id = result.get("id")
                resource_id = result.get("resourceId")

                # Store channel info for management (like your active_channels)
                self._active_channels[channel_id] = {
                    "resource_id": resource_id,
                    "expiration": expiration,
                    "trigger_id": trigger_config.trigger_id,
                    "user_id": trigger_config.user_id,
                    "calendar_id": calendar_id,
                    "created_at": datetime.now(timezone.utc).timestamp(),
                }

                logger.info(
                    f"Created webhook subscription for user {trigger_config.user_id}",
                    channel_id=channel_id,
                    resource_id=resource_id,
                    calendar_id=calendar_id,
                    expires_at=datetime.fromtimestamp(
                        expiration / 1000, timezone.utc
                    ).isoformat(),
                )
                return channel_id  # Return channel_id instead of subscription_id

            return None

        except asyncio.TimeoutError:
            logger.error("Timeout creating webhook subscription")
            return None
        except Exception as e:
            logger.error(f"Failed to create webhook subscription", error=str(e))
            return None

    async def _execute_watch_request(
        self, service, calendar_id: str, body: Dict[str, Any]
    ):
        """
        Execute the Google Calendar watch request.

        Args:
            service: Google Calendar service instance
            calendar_id: Calendar ID to watch
            body: Request body for the watch request

        Returns:
            Dict: Response from the watch request
        """
        try:
            # Execute the watch request asynchronously
            result = await asyncio.to_thread(
                lambda: service.events()
                .watch(calendarId=calendar_id, body=body)
                .execute()
            )
            return result

        except HttpError as e:
            if e.resp.status in [500, 502, 503, 504]:
                # Retryable HTTP errors
                raise GoogleCalendarAPIError(f"Google Calendar API error: {str(e)}")
            else:
                # Non-retryable errors
                raise GoogleCalendarError(f"Google Calendar API error: {str(e)}")
        except Exception as e:
            raise GoogleCalendarAPIError(f"Unexpected error: {str(e)}")

    async def _remove_webhook_subscription(
        self, service, channel_id: str, resource_id: Optional[str] = None
    ) -> bool:
        """
        Remove a webhook subscription.

        Args:
            service: Google Calendar service instance
            channel_id: ID of the channel to stop
            resource_id: Resource ID of the subscription (optional)

        Returns:
            bool: True if removal was successful
        """
        try:
            # Stop the webhook subscription
            body = {
                "id": channel_id,
            }

            # Add resource ID if provided
            if resource_id:
                body["resourceId"] = resource_id

            result = await asyncio.to_thread(
                lambda: service.channels().stop(body=body).execute()
            )

            logger.info(f"Successfully removed webhook subscription {channel_id}")
            return True

        except HttpError as e:
            if e.resp.status == 404:
                # Subscription already removed or doesn't exist
                logger.info(
                    f"Webhook subscription {channel_id} not found (already removed)"
                )
                return True
            else:
                logger.error(
                    f"Failed to remove webhook subscription {channel_id}",
                    error=str(e),
                )
                return False
        except Exception as e:
            logger.error(
                f"Failed to remove webhook subscription {channel_id}", error=str(e)
            )
            return False

    def _validate_webhook(
        self, headers: Dict[str, str], event_data: Dict[str, Any]
    ) -> bool:
        """
        Validate Google Calendar webhook authenticity.
        Enhanced based on your webhook implementation with signature verification.

        Args:
            headers: Webhook headers
            event_data: Event data

        Returns:
            bool: True if webhook is valid
        """
        try:
            # Basic validation - check for required headers
            required_headers = ["x-goog-channel-id", "x-goog-resource-state"]
            for header in required_headers:
                if header not in headers:
                    logger.warning(f"Missing required header: {header}")
                    return False

            # Validate resource state
            resource_state = headers.get("x-goog-resource-state")
            valid_states = ["exists", "not_exists", "sync"]
            if resource_state not in valid_states:
                logger.warning(f"Invalid resource state: {resource_state}")
                return False

            # Verify webhook signature if secret is configured (like your implementation)
            webhook_secret = getattr(
                self.settings, "google_calendar_webhook_secret", None
            )
            if webhook_secret:
                signature = headers.get("x-goog-channel-token")
                payload = event_data.get("raw_payload", b"")

                if signature and not self._verify_webhook_signature(
                    payload, signature, webhook_secret
                ):
                    logger.warning("Invalid webhook signature")
                    return False

            logger.debug("Webhook validation passed")
            return True

        except Exception as e:
            logger.error(f"Webhook validation failed", error=str(e))
            return False

    def _verify_webhook_signature(
        self, payload: bytes, signature: str, secret: str
    ) -> bool:
        """
        Verify the webhook signature for security.
        Based on your verify_webhook_signature function.

        Args:
            payload: Raw payload bytes
            signature: Signature from headers
            secret: Webhook secret

        Returns:
            bool: True if signature is valid
        """
        try:
            if not signature or not secret:
                return True  # Skip verification if no secret is configured

            import hmac
            import hashlib

            expected_signature = hmac.new(
                secret.encode("utf-8"), payload, hashlib.sha256
            ).hexdigest()

            return hmac.compare_digest(signature, expected_signature)

        except Exception as e:
            logger.error(f"Signature verification failed", error=str(e))
            return False

    async def _parse_calendar_event(
        self, raw_event: Dict[str, Any], headers: Dict[str, str]
    ) -> Optional[Dict[str, Any]]:
        """
        Parse Google Calendar webhook event into standardized format.

        Args:
            raw_event: Raw event data from webhook
            headers: Webhook headers

        Returns:
            Dict: Parsed event data, or None if event should be ignored
        """
        try:
            # Extract information from headers
            channel_id = headers.get("x-goog-channel-id", "")
            resource_state = headers.get("x-goog-resource-state", "")
            resource_id = headers.get("x-goog-resource-id", "")

            # Determine event type based on resource state
            event_type_mapping = {
                "exists": TriggerEventType.UPDATED,  # Event created or updated
                "not_exists": TriggerEventType.DELETED,  # Event deleted
                "sync": TriggerEventType.TRIGGERED,  # Sync event (ignore)
            }

            event_type = event_type_mapping.get(resource_state)
            if not event_type or event_type == TriggerEventType.TRIGGERED:
                logger.debug(f"Ignoring sync event with state: {resource_state}")
                return None

            # Generate event ID
            event_id = f"{channel_id}-{resource_id}-{datetime.now().isoformat()}"

            # Extract calendar information from channel ID
            # Channel ID format: "trigger-{trigger_id}"
            trigger_id_part = (
                channel_id.replace("trigger-", "")
                if channel_id.startswith("trigger-")
                else ""
            )

            # Build event data
            event_data = {
                "event_id": event_id,
                "event_type": event_type,
                "timestamp": datetime.now(),
                "data": {
                    "channel_id": channel_id,
                    "resource_state": resource_state,
                    "resource_id": resource_id,
                    "trigger_id": trigger_id_part,
                    "calendar_event": raw_event.get("calendar_event", {}),
                },
                "metadata": {
                    "webhook_headers": headers,
                    "raw_event": raw_event,
                },
            }

            # Apply event filters if configured
            if not self._should_process_event(event_data, raw_event):
                logger.debug("Event filtered out by configuration")
                return None

            logger.debug(f"Successfully parsed calendar event {event_id}")
            return event_data

        except Exception as e:
            logger.error(f"Failed to parse calendar event", error=str(e))
            return None

    def _should_process_event(
        self, event_data: Dict[str, Any], raw_event: Dict[str, Any]
    ) -> bool:
        """
        Check if event should be processed based on filters.

        Args:
            event_data: Parsed event data
            raw_event: Raw event data

        Returns:
            bool: True if event should be processed
        """
        try:
            # For now, process all events
            # In the future, this could check trigger-specific filters
            # like event title patterns, attendee filters, etc.

            # Basic validation - ensure we have required data
            if not event_data.get("event_id") or not event_data.get("event_type"):
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking event filters", error=str(e))
            return False

    async def get_subscription_info(self, trigger_id: UUID) -> Optional[Dict[str, Any]]:
        """
        Get webhook subscription information for a trigger.

        Args:
            trigger_id: Trigger ID

        Returns:
            Dict: Subscription information, or None if not found
        """
        return self._webhook_subscriptions.get(trigger_id)

    async def renew_subscription(self, trigger_id: UUID) -> bool:
        """
        Renew a webhook subscription that is about to expire.

        Args:
            trigger_id: Trigger ID

        Returns:
            bool: True if renewal was successful
        """
        try:
            subscription_info = self._webhook_subscriptions.get(trigger_id)
            if not subscription_info:
                logger.error(f"No subscription found for trigger {trigger_id}")
                return False

            # Get user credentials
            credentials = await self._get_user_credentials(subscription_info["user_id"])
            if not credentials:
                logger.error(f"Failed to get credentials for subscription renewal")
                return False

            # Create service
            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error("Failed to create service for subscription renewal")
                return False

            # Remove old subscription
            await self._remove_webhook_subscription(
                service,
                subscription_info["subscription_id"],
                subscription_info.get("resource_id"),
            )

            # Create new subscription (this would need trigger config)
            # For now, just log that renewal is needed
            logger.info(f"Subscription renewal needed for trigger {trigger_id}")

            return True

        except Exception as e:
            logger.error(
                f"Failed to renew subscription for trigger {trigger_id}", error=str(e)
            )
            return False

    def _read_json_file(self, file_path) -> dict:
        """
        Synchronous helper method to read JSON file.
        This is called via asyncio.to_thread to avoid blocking.
        """
        import json

        with open(file_path, "r") as f:
            return json.load(f)

    def _write_json_file(self, file_path, data: dict) -> None:
        """
        Synchronous helper method to write JSON file.
        This is called via asyncio.to_thread to avoid blocking.
        """
        import json

        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)

    async def _setup_polling_trigger(
        self, trigger_config: TriggerConfiguration, service
    ) -> bool:
        """
        Set up polling-based trigger monitoring (fallback when webhooks fail).
        Based on the POC polling approach.

        Args:
            trigger_config: Trigger configuration
            service: Google Calendar service instance

        Returns:
            bool: True if polling setup was successful
        """
        try:
            trigger_id = trigger_config.trigger_id
            poll_interval = trigger_config.config.get("poll_interval_seconds", 60)

            # Initialize polling state
            self._polling_states[trigger_id] = {
                "last_check_time": datetime.now(timezone.utc).isoformat(),
                "calendar_id": trigger_config.config["calendar_id"],
                "user_id": trigger_config.user_id,
                "event_types": trigger_config.event_types,
                "poll_interval": poll_interval,
                "created_at": datetime.now(),
            }

            # Start polling task
            task = asyncio.create_task(
                self._polling_loop(trigger_id, trigger_config, service)
            )
            self._polling_tasks[trigger_id] = task

            logger.info(
                "🔄 Started Google Calendar polling",
                trigger_id=trigger_id,
                user_id=trigger_config.user_id,
                poll_interval=poll_interval,
                calendar_id=trigger_config.config["calendar_id"],
            )
            return True

        except Exception as e:
            logger.error(f"Failed to setup polling trigger {trigger_id}", error=str(e))
            return False

    async def _polling_loop(
        self, trigger_id: UUID, trigger_config: TriggerConfiguration, service
    ):
        """
        Main polling loop for a trigger (based on your POC).
        Continuously checks for new calendar events.

        Args:
            trigger_id: Trigger ID
            trigger_config: Trigger configuration
            service: Google Calendar service instance
        """
        try:
            poll_interval = self._polling_states[trigger_id]["poll_interval"]
            calendar_id = self._polling_states[trigger_id]["calendar_id"]

            logger.info(f"Starting polling loop for trigger {trigger_id}")

            while trigger_id in self._polling_states:
                try:
                    # Get current state
                    state = self._polling_states[trigger_id]
                    last_check_time_str = state["last_check_time"]
                    last_check_time_dt = datetime.fromisoformat(
                        last_check_time_str.replace("Z", "+00:00")
                    )

                    logger.debug(
                        f"Checking for new events for trigger {trigger_id}",
                        last_check_time=last_check_time_str,
                    )

                    # Find newly created events (based on your POC logic)
                    newly_created_events = await self._get_newly_created_events(
                        service, calendar_id, last_check_time_dt
                    )

                    if newly_created_events:
                        logger.info(
                            "📅 Found new calendar events",
                            trigger_id=trigger_id,
                            user_id=trigger_config.user_id,
                            event_count=len(newly_created_events),
                            calendar_id=calendar_id,
                        )

                        latest_creation_time = last_check_time_dt

                        for event in newly_created_events:
                            # Process each event
                            trigger_event = (
                                await self._create_trigger_event_from_calendar_event(
                                    event, trigger_id, "created"
                                )
                            )

                            if trigger_event:
                                # Send to trigger manager for processing
                                await self._emit_trigger_event(trigger_event)

                            # Update latest creation time
                            event_created_dt = datetime.fromisoformat(
                                event["created"].replace("Z", "+00:00")
                            )
                            if event_created_dt > latest_creation_time:
                                latest_creation_time = event_created_dt

                        # Update state with latest processed time
                        new_state_time = latest_creation_time.isoformat().replace(
                            "+00:00", "Z"
                        )
                        self._polling_states[trigger_id][
                            "last_check_time"
                        ] = new_state_time

                        logger.debug(
                            f"Updated polling state for trigger {trigger_id}",
                            new_last_check_time=new_state_time,
                        )

                    # Wait for next poll
                    await asyncio.sleep(poll_interval)

                except asyncio.CancelledError:
                    logger.info(f"Polling loop cancelled for trigger {trigger_id}")
                    break
                except Exception as e:
                    logger.error(
                        f"Error in polling loop for trigger {trigger_id}",
                        error=str(e),
                    )
                    # Wait longer after an error
                    await asyncio.sleep(poll_interval * 2)

        except Exception as e:
            logger.error(
                f"Fatal error in polling loop for trigger {trigger_id}",
                error=str(e),
            )
        finally:
            # Clean up
            if trigger_id in self._polling_tasks:
                del self._polling_tasks[trigger_id]
            logger.info(f"Polling loop ended for trigger {trigger_id}")

    async def _get_newly_created_events(
        self, service, calendar_id: str, last_check_time_dt: datetime
    ) -> List[Dict[str, Any]]:
        """
        Get newly created calendar events since the last check time.
        Based on your POC logic for finding new events.

        Args:
            service: Google Calendar service instance
            calendar_id: Calendar ID to check
            last_check_time_dt: Last check timestamp

        Returns:
            List[Dict]: List of newly created events
        """
        try:
            # Get all upcoming events (similar to your POC)
            now_utc = datetime.now(timezone.utc).isoformat()
            events_result = await asyncio.to_thread(
                lambda: service.events()
                .list(
                    calendarId=calendar_id,
                    timeMin=now_utc,
                    singleEvents=True,
                    orderBy="startTime",
                    maxResults=50,  # Reasonable limit
                )
                .execute()
            )

            all_upcoming_events = events_result.get("items", [])
            newly_created_events = []

            # Filter by creation time (your POC logic)
            for event in all_upcoming_events:
                if "created" not in event:
                    continue

                created_str = event["created"]
                created_dt = datetime.fromisoformat(created_str.replace("Z", "+00:00"))

                if created_dt > last_check_time_dt:
                    newly_created_events.append(event)

            return newly_created_events

        except Exception as e:
            logger.error(f"Failed to get newly created events", error=str(e))
            return []

    async def _create_trigger_event_from_calendar_event(
        self, calendar_event: Dict[str, Any], trigger_id: UUID, event_type: str
    ) -> Optional[TriggerEvent]:
        """
        Create a standardized trigger event from a Google Calendar event.

        Args:
            calendar_event: Google Calendar event data
            trigger_id: Trigger ID that detected this event
            event_type: Type of event (created, updated, etc.)

        Returns:
            TriggerEvent: Standardized trigger event
        """
        try:
            # Generate event ID
            event_id = f"gcal-{calendar_event.get('id', 'unknown')}-{trigger_id}"

            # Extract event details
            summary = calendar_event.get("summary", "Untitled Event")
            start_time = calendar_event.get("start", {}).get(
                "dateTime", calendar_event.get("start", {}).get("date")
            )
            created_time = calendar_event.get("created")

            # Create trigger event
            trigger_event = TriggerEvent(
                event_id=event_id,
                event_type=(
                    TriggerEventType.CREATED
                    if event_type == "created"
                    else TriggerEventType.UPDATED
                ),
                source="google_calendar",
                timestamp=(
                    datetime.fromisoformat(created_time.replace("Z", "+00:00"))
                    if created_time
                    else datetime.now(timezone.utc)
                ),
                data={
                    "calendar_event": calendar_event,
                    "summary": summary,
                    "start_time": start_time,
                    "trigger_id": str(trigger_id),
                    "detection_method": "polling",
                },
                metadata={
                    "google_calendar_event_id": calendar_event.get("id"),
                    "calendar_id": calendar_event.get("organizer", {}).get(
                        "email", "unknown"
                    ),
                    "event_type": event_type,
                },
            )

            logger.debug(
                f"Created trigger event from calendar event",
                event_id=event_id,
                summary=summary,
                trigger_id=trigger_id,
            )

            return trigger_event

        except Exception as e:
            logger.error(
                f"Failed to create trigger event from calendar event",
                error=str(e),
                calendar_event_id=calendar_event.get("id"),
            )
            return None

    async def _emit_trigger_event(self, trigger_event: TriggerEvent):
        """
        Emit a trigger event for processing by the trigger manager.
        This is a placeholder - in a real implementation, this would
        send the event to the trigger manager or event queue.

        Args:
            trigger_event: Trigger event to emit
        """
        try:
            # For now, just log the event
            # In a real implementation, this would send to the trigger manager
            logger.info(
                "🎯 TRIGGER EVENT FIRED",
                event_type=trigger_event.event_type.value,
                event_title=trigger_event.data.get("summary"),
                event_id=trigger_event.event_id,
                source=trigger_event.source,
                trigger_id=trigger_event.data.get("trigger_id"),
                detection_method=trigger_event.data.get("detection_method"),
                timestamp=trigger_event.timestamp.isoformat(),
            )

            # TODO: Integrate with actual trigger manager event processing
            # await self.trigger_manager.process_trigger_event(trigger_event)

        except Exception as e:
            logger.error(f"Failed to emit trigger event", error=str(e))

    async def get_webhook_status(self) -> Dict[str, Any]:
        """
        Get the status of active webhook channels.
        Based on your webhook_status endpoint.

        Returns:
            Dict: Status information for all active channels
        """
        try:
            from datetime import datetime, timezone

            status = []
            current_time = datetime.now(timezone.utc).timestamp() * 1000

            for channel_id, channel_info in self._active_channels.items():
                expiration = channel_info["expiration"]
                is_expired = current_time > expiration

                status.append(
                    {
                        "channel_id": channel_id,
                        "resource_id": channel_info["resource_id"],
                        "trigger_id": str(channel_info["trigger_id"]),
                        "user_id": channel_info["user_id"],
                        "calendar_id": channel_info["calendar_id"],
                        "expires_at": datetime.fromtimestamp(
                            expiration / 1000, timezone.utc
                        ).isoformat(),
                        "is_expired": is_expired,
                        "created_at": datetime.fromtimestamp(
                            channel_info["created_at"], timezone.utc
                        ).isoformat(),
                    }
                )

            return {
                "active_channels": status,
                "total_channels": len(status),
                "expired_channels": sum(1 for s in status if s["is_expired"]),
            }

        except Exception as e:
            logger.error(f"Failed to get webhook status", error=str(e))
            return {"error": str(e)}

    async def cleanup_expired_channels(self) -> int:
        """
        Clean up expired webhook channels.
        Based on your cleanup_expired_channels function.

        Returns:
            int: Number of channels cleaned up
        """
        try:
            from datetime import datetime, timezone

            current_time = datetime.now(timezone.utc).timestamp() * 1000
            expired_channels = []

            # Find expired channels
            for channel_id, channel_info in self._active_channels.items():
                if current_time > channel_info["expiration"]:
                    expired_channels.append((channel_id, channel_info))

            # Clean up expired channels
            cleaned_count = 0
            for channel_id, channel_info in expired_channels:
                try:
                    # Get user credentials and service
                    user_id = channel_info["user_id"]
                    credentials = await self._get_user_credentials(user_id)
                    if credentials:
                        service = await self._create_calendar_service(credentials)
                        if service:
                            # Stop the webhook subscription
                            await self._remove_webhook_subscription(
                                service, channel_id, channel_info["resource_id"]
                            )

                    # Remove from active channels
                    del self._active_channels[channel_id]
                    cleaned_count += 1

                    logger.info(f"Cleaned up expired channel: {channel_id}")

                except Exception as e:
                    logger.error(
                        f"Failed to cleanup channel {channel_id}", error=str(e)
                    )

            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} expired webhook channels")

            return cleaned_count

        except Exception as e:
            logger.error(f"Error during channel cleanup", error=str(e))
            return 0

    async def stop_webhook_channel(self, channel_id: str) -> bool:
        """
        Stop a specific webhook channel.
        Based on your stop_webhook endpoint.

        Args:
            channel_id: Channel ID to stop

        Returns:
            bool: True if successfully stopped
        """
        try:
            if channel_id not in self._active_channels:
                logger.warning(f"Channel not found: {channel_id}")
                return False

            channel_info = self._active_channels[channel_id]
            user_id = channel_info["user_id"]
            resource_id = channel_info["resource_id"]

            # Get user credentials and service
            credentials = await self._get_user_credentials(user_id)
            if not credentials:
                logger.error(f"No credentials found for user {user_id}")
                return False

            service = await self._create_calendar_service(credentials)
            if not service:
                logger.error(f"Failed to create service for user {user_id}")
                return False

            # Stop the webhook subscription
            success = await self._remove_webhook_subscription(
                service, channel_id, resource_id
            )

            if success:
                # Remove from active channels
                del self._active_channels[channel_id]
                logger.info(f"Successfully stopped webhook channel: {channel_id}")

            return success

        except Exception as e:
            logger.error(f"Failed to stop webhook channel {channel_id}", error=str(e))
            return False
